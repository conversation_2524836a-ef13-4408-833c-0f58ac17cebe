/* 王国争霸H5 - UI组件样式 */

/* 侧边面板 */
.side-panel {
    position: fixed;
    top: 80px;
    right: -400px;
    width: 380px;
    height: calc(100vh - 180px);
    background: rgba(0,0,0,0.95);
    color: white;
    border-radius: 15px 0 0 15px;
    box-shadow: -5px 0 20px rgba(0,0,0,0.5);
    transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.side-panel.show {
    right: 0;
    box-shadow: -10px 0 30px rgba(0,0,0,0.7);
}

/* 面板遮罩层 */
.panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.3);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.panel-overlay.show {
    opacity: 1;
    visibility: visible;
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 15px 0 0 0;
}

.panel-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.panel-content {
    padding: 20px;
    height: calc(100% - 80px);
    overflow-y: auto;
}

/* 建造面板 */
.building-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 8px 16px;
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.category-btn.active,
.category-btn:hover {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    transform: translateY(-1px);
}

.building-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.building-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.building-item:hover {
    background: rgba(255,255,255,0.2);
    border-color: #4CAF50;
    transform: translateY(-2px);
}

.building-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.building-item.disabled:hover {
    transform: none;
    border-color: transparent;
}

.building-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.building-name {
    font-weight: bold;
    color: #FFD700;
}

.building-cost {
    font-size: 0.9rem;
    color: #ccc;
}

.building-desc {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 10px;
}

.building-stats {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #aaa;
}

/* 解锁条件样式 */
.unlock-requirement {
    margin-top: 10px;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    font-weight: bold;
    text-align: center;
}

.unlock-requirement.level-locked {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.unlock-requirement.resource-locked {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.unlock-requirement.unlocked {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.unlock-requirement.building-locked {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.unlock-requirement.researched {
    background: rgba(23, 162, 184, 0.2);
    color: #17a2b8;
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.unlock-requirement.researching {
    background: rgba(102, 16, 242, 0.2);
    color: #6610f2;
    border: 1px solid rgba(102, 16, 242, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* 战斗界面样式 */
.battle-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.95);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-bottom: 2px solid #3498db;
}

.battle-header h2 {
    color: #ecf0f1;
    margin: 0;
    font-size: 1.5rem;
}

.battle-header #battleRound {
    color: #f39c12;
    font-weight: bold;
    font-size: 1.2rem;
}

.close-battle-btn {
    background: #e74c3c;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: background 0.3s;
}

.close-battle-btn:hover {
    background: #c0392b;
}

.battle-field {
    flex: 1;
    display: flex;
    padding: 20px;
    gap: 20px;
}

.battle-side {
    flex: 1;
    background: rgba(52, 73, 94, 0.8);
    border-radius: 10px;
    padding: 15px;
}

.battle-side h3 {
    color: #ecf0f1;
    margin: 0 0 15px 0;
    text-align: center;
    font-size: 1.2rem;
}

.player-side {
    border: 2px solid #27ae60;
}

.enemy-side {
    border: 2px solid #e74c3c;
}

.battle-unit-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.battle-unit {
    background: rgba(44, 62, 80, 0.9);
    border-radius: 8px;
    padding: 10px;
    border-left: 4px solid #3498db;
    transition: all 0.3s;
}

.battle-unit.active {
    border-left-color: #f39c12;
    background: rgba(241, 196, 15, 0.2);
    transform: scale(1.05);
}

.battle-unit.dead {
    opacity: 0.3;
    border-left-color: #95a5a6;
}

.unit-name {
    color: #ecf0f1;
    font-weight: bold;
    margin-bottom: 5px;
}

.unit-health {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.health-bar {
    flex: 1;
    height: 8px;
    background: #34495e;
    border-radius: 4px;
    overflow: hidden;
}

.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #27ae60);
    transition: width 0.5s;
}

.unit-stats {
    display: flex;
    gap: 15px;
    font-size: 0.8rem;
    color: #bdc3c7;
}

.battle-center {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.battle-log {
    flex: 1;
    background: rgba(44, 62, 80, 0.9);
    border-radius: 10px;
    padding: 15px;
    overflow-y: auto;
    max-height: 400px;
}

.log-entry {
    color: #ecf0f1;
    margin-bottom: 8px;
    padding: 5px 10px;
    border-radius: 5px;
    background: rgba(52, 73, 94, 0.5);
    animation: fadeIn 0.5s;
}

.log-entry.damage {
    border-left: 3px solid #e74c3c;
}

.log-entry.heal {
    border-left: 3px solid #27ae60;
}

.log-entry.info {
    border-left: 3px solid #3498db;
}

.log-entry.critical {
    border-left: 3px solid #f39c12;
    background: rgba(241, 196, 15, 0.2);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.battle-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 20px;
    background: rgba(44, 62, 80, 0.8);
    border-top: 1px solid #34495e;
}

.battle-controls .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s;
}

.battle-controls .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

/* 标签页 */
.army-tabs,
.quest-tabs {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(255,255,255,0.1);
}

.tab-btn {
    padding: 10px 20px;
    background: none;
    color: #ccc;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
    font-size: 1rem;
}

.tab-btn.active,
.tab-btn:hover {
    color: white;
    border-bottom-color: #4CAF50;
}

.tab-content {
    min-height: 300px;
}

/* 军队面板 */
.unit-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.unit-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.unit-info {
    flex: 1;
}

.unit-name {
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 5px;
}

.unit-stats {
    font-size: 0.9rem;
    color: #ccc;
}

.unit-actions {
    display: flex;
    gap: 10px;
}

/* 科技树 */
.tech-tree {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.tech-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
}

.tech-item:hover {
    background: rgba(255,255,255,0.2);
    border-color: #4CAF50;
}

.tech-item.researched {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border-color: #4CAF50;
}

.tech-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tech-name {
    font-weight: bold;
    margin-bottom: 10px;
    color: #FFD700;
}

.tech-desc {
    font-size: 0.8rem;
    color: #ccc;
    margin-bottom: 10px;
}

.tech-cost {
    font-size: 0.8rem;
    color: #aaa;
}

/* 战斗面板 */
.battle-modes {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.mode-btn {
    padding: 10px 20px;
    background: rgba(255,255,255,0.1);
    color: white;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn.active,
.mode-btn:hover {
    background: linear-gradient(45deg, #FF6B6B, #FF5252);
}

.battle-content {
    min-height: 300px;
}

.battle-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.battle-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.battle-item:hover {
    background: rgba(255,255,255,0.2);
    border-color: #FF6B6B;
}

.battle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.battle-name {
    font-weight: bold;
    color: #FFD700;
}

.battle-difficulty {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.difficulty-tutorial { background: #17a2b8; }
.difficulty-easy { background: #4CAF50; }
.difficulty-normal { background: #FF9800; }
.difficulty-hard { background: #F44336; }

.battle-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.battle-item.disabled:hover {
    background: rgba(255,255,255,0.1);
    border-color: transparent;
}

/* 军队状态显示 */
.army-status {
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 12px;
    margin: 8px 0;
    font-weight: bold;
}

.army-status.ready {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.army-status.training {
    background: rgba(241, 196, 15, 0.2);
    color: #f1c40f;
    border: 1px solid rgba(241, 196, 15, 0.3);
}

.army-status.none {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* 解锁条件样式优化 */
.unlock-requirement.army-locked {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* 任务面板 */
.quest-list {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.quest-item {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 15px;
    border-left: 4px solid #4CAF50;
}

.quest-item.completed {
    border-left-color: #FFD700;
    background: rgba(255, 215, 0, 0.1);
}

.quest-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.quest-name {
    font-weight: bold;
    color: #FFD700;
}

.quest-reward {
    font-size: 0.9rem;
    color: #4CAF50;
}

.quest-desc {
    font-size: 0.9rem;
    color: #ccc;
    margin-bottom: 10px;
}

.quest-progress {
    font-size: 0.8rem;
    color: #aaa;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: opacity 0.3s ease;
}

.modal.hidden {
    opacity: 0;
    pointer-events: none;
}

.modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.5);
}

.modal-content h3,
.modal-content h4 {
    margin-bottom: 20px;
    color: #FFD700;
}

.menu-options {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.option-btn {
    padding: 12px 24px;
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.option-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-1px);
}

.dialog-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #757575;
    color: white;
}

.btn-secondary:hover {
    background: #616161;
}

/* 消息提示 */
.message-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 3000;
    max-width: 300px;
}

.message {
    background: rgba(0,0,0,0.9);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    transform: translateX(100%);
    animation: slideIn 0.3s ease forwards;
}

.message.success {
    border-left: 4px solid #4CAF50;
}

.message.error {
    border-left: 4px solid #F44336;
}

.message.info {
    border-left: 4px solid #2196F3;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .side-panel {
        width: 100%;
        right: -100%;
        top: 70px;
        height: calc(100vh - 150px);
        border-radius: 0;
    }
    
    .panel-header {
        border-radius: 0;
    }
    
    .building-categories,
    .battle-modes {
        gap: 5px;
    }
    
    .category-btn,
    .mode-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
    }
    
    .modal-content {
        padding: 20px;
        margin: 20px;
    }
}
